"""
PandasAI v3 配置文件 - 严格遵循官方API规范
基于官方文档: https://docs.pandas-ai.com/v3/introduction
"""
import os
from pathlib import Path
from dotenv import load_dotenv

# 确保从正确的路径加载环境变量
env_path = Path(__file__).parent / '.env'
load_dotenv(env_path, override=True)

class ConfigV3:
    """PandasAI v3 配置类 - 遵循官方文档规范"""
    
    # API配置（使用阿里云百炼官方环境变量名称）
    DASHSCOPE_API_KEY = os.getenv("DASHSCOPE_API_KEY", "")
    LLM_MODEL = os.getenv("LLM_MODEL", "dashscope/qwen-max")  # LiteLLM需要提供商前缀
    LLM_TEMPERATURE = float(os.getenv("LLM_TEMPERATURE", "0.1"))
    LLM_MAX_TOKENS = int(os.getenv("LLM_MAX_TOKENS", "4000"))
    
    # PandasAI v3 全局配置 - 基于官方文档 /v3/overview-nl
    PANDASAI_CONFIG = {
        "save_logs": True,          # 保存LLM日志
        "verbose": False,           # 控制台输出详细信息
        "max_retries": 3,          # 错误重试次数
        "temperature": LLM_TEMPERATURE  # LLM温度参数
    }
    
    # 语义层配置 - 基于官方文档 /v3/semantic-layer
    SEMANTIC_LAYER_PATH = "datasets"  # 语义数据集存储路径
    
    # 应用配置
    APP_TITLE = "智能财务数据分析平台 v3.0 (PandasAI v3)"
    UPLOAD_DIRECTORY = "uploaded_files"
    EXPORTS_DIRECTORY = "exports"
    MAX_FILE_SIZE_MB = int(os.getenv("MAX_FILE_SIZE_MB", "100"))
    
    # 日志配置
    LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
    LOG_FILE = os.getenv("LOG_FILE", "app.log")
    
    # 支持的文件类型
    SUPPORTED_FILE_TYPES = ["csv", "xlsx", "xls"]
    
    # 财务数据语义模板 - 用于创建语义数据集
    FINANCIAL_SCHEMAS = {
        "revenue": {
            "description": "收入数据分析 - 包含收入相关的财务数据",
            "suggested_columns": ["date", "amount", "category", "region", "product"]
        },
        "expenses": {
            "description": "支出数据分析 - 包含支出和成本相关数据",
            "suggested_columns": ["date", "amount", "category", "department", "vendor"]
        },
        "balance_sheet": {
            "description": "资产负债表数据 - 包含资产、负债和权益数据",
            "suggested_columns": ["date", "assets", "liabilities", "equity"]
        },
        "general": {
            "description": "通用财务数据分析",
            "suggested_columns": []
        }
    }
    
    # 引导话术
    GUIDANCE_MESSAGE = """
    我是基于PandasAI v3的智能数据分析助手，专门帮助您分析财务数据。
    
    v3新功能特性：
    - 🧠 语义数据层：更智能的数据理解
    - 🚀 增强AI分析：更准确的自然语言处理
    - 📊 多种输出格式：文本、图表、数据表格
    - 🎯 多数据集支持：跨数据集分析能力
    
    请提出与您上传数据相关的问题，例如：
    - "显示前10行数据"
    - "哪个产品的销售额最高？"
    - "按月份绘制销售趋势图"
    - "计算各类别的平均值"
    - "找出异常值"
    """
    
    @classmethod
    def validate_config(cls):
        """验证配置是否完整"""
        if not cls.DASHSCOPE_API_KEY:
            raise ValueError("DASHSCOPE_API_KEY 未设置，请在.env文件中配置您的API密钥")
        
        # 创建必要目录
        directories = [
            cls.UPLOAD_DIRECTORY, 
            cls.EXPORTS_DIRECTORY, 
            cls.SEMANTIC_LAYER_PATH,
            f"{cls.EXPORTS_DIRECTORY}/charts"
        ]
        
        for directory in directories:
            if not os.path.exists(directory):
                os.makedirs(directory)
                
        return True
    
    @classmethod
    def get_financial_schema(cls, dataset_type: str) -> dict:
        """获取财务数据模式"""
        return cls.FINANCIAL_SCHEMAS.get(dataset_type, cls.FINANCIAL_SCHEMAS["general"])
